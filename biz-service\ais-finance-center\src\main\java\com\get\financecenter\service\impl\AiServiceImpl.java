package com.get.financecenter.service.impl;

import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.service.IAiService;
import com.get.financecenter.service.IExpenseClaimFormService;
import com.get.financecenter.service.TravelClaimFormService;
import com.get.financecenter.vo.ExpenseClaimFormVo;
import com.get.financecenter.vo.ExpenseReimbursementVo;
import com.get.financecenter.vo.ReimbursementDetailVo;
import com.get.financecenter.vo.TravelClaimFormVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class AiServiceImpl implements IAiService {
    @Resource
    private TravelClaimFormService travelClaimFormService;
    @Resource
    private IExpenseClaimFormService expenseClaimFormService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    /**
     * 根据token 查询自己的费用报销单和差旅报销单
     * @return
     */
    @Override
    public ExpenseReimbursementVo getMyExpenseReimbursement() {
        Long staffId = SecureUtil.getStaffInfo().getStaffId();
        ExpenseReimbursementVo expenseReimbursementVo = new ExpenseReimbursementVo();
        String name = permissionCenterClient.getStaffName(staffId).getData();
        if (name != null) {
            expenseReimbursementVo.setName(name);
        }

        List<TravelClaimFormVo> travelClaimFormVos = travelClaimFormService.getAllTravelClaimForms(0);
        List<ExpenseClaimFormVo> expenseClaimFormVos = expenseClaimFormService.getAllExpenseClaimForms(0);
        ReimbursementDetailVo reimbursementDetailVo = new ReimbursementDetailVo();
        //差旅报销单
        expenseReimbursementVo.setReimbursementDetailVo(reimbursementDetailVo);
        if (GeneralTool.isNotEmpty(travelClaimFormVos)) {
            reimbursementDetailVo.setTravelClaimFormVos(travelClaimFormVos);
        }
        //费用报销单
        if (GeneralTool.isNotEmpty(expenseClaimFormVos)) {
            reimbursementDetailVo.setExpenseClaimFormVos(expenseClaimFormVos);
        }
//        expenseReimbursementVo.setStaffId(staffId);
        return expenseReimbursementVo;
    }
}
