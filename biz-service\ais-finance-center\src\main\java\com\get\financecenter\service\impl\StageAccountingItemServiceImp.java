package com.get.financecenter.service.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.GetDateUtil;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.dao.CompanyAccountingItemMapper;
import com.get.financecenter.dao.StageAccountingItemValueMapper;
import com.get.financecenter.dao.StageValueTaskQueueMapper;
import com.get.financecenter.dao.VouchMapper;
import com.get.financecenter.dto.ReCalculateSubjectBalanceSummaryDto;
import com.get.financecenter.dto.StageAccountingItemDto;
import com.get.financecenter.entity.AccountingItem;
import com.get.financecenter.entity.StageAccountingItemValue;
import com.get.financecenter.entity.StageValueTaskQueue;
import com.get.financecenter.enums.AccountingTypeEnum;
import com.get.financecenter.service.AccountingItemService;
import com.get.financecenter.service.StageAccountingItemService;
import com.get.financecenter.vo.CompanyAccountingItemVo;
import com.get.financecenter.vo.StageAccountingItemVo;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class StageAccountingItemServiceImp implements StageAccountingItemService {

    @Resource
    private StageAccountingItemValueMapper stageAccountingItemValueMapper;
    @Resource
    private CompanyAccountingItemMapper companyAccountingItemMapper;
    @Resource
    private AccountingItemMapper accountingItemMapper;
    @Resource
    private AccountingItemService accountingItemService;
    @Resource
    private VouchMapper vouchMapper;
    @Resource
    private StageValueTaskQueueMapper stageValueTaskQueueMapper;
    @Resource
    @Lazy
    private StageAccountingItemService stageAccountingItemService;


    /**
     * 生成并查询科目余额汇总表
     *
     * @param stageAccountingItemDto
     * @return
     */
    @Override
    @Transactional
    public List<StageAccountingItemVo> createStageAccountingItem(StageAccountingItemDto stageAccountingItemDto) {
        // 获取从传入时间到当前时间的所有YearMonth集合
        List<YearMonth> yearMonthList = GetDateUtil.getYearMonthRange(stageAccountingItemDto.getStartTime(), stageAccountingItemDto.getEndTime());
        for (YearMonth yearMonth : yearMonthList) {
            createStageAccountingItemValue(stageAccountingItemDto.getCompanyId(), yearMonth.getYear(), yearMonth.getMonthValue(), SecureUtil.getStaffInfo(), stageAccountingItemDto.getGrade());
        }
        //构建返回结果
        List<StageAccountingItemVo>  stageAccountingItemVoList = stageAccountingItemValueMapper.getStageAccountingItemValueList(stageAccountingItemDto);
        stageAccountingItemVoList.stream().forEach(item -> {
            item.setTypeName(AccountingTypeEnum.getNameById(item.getType()));
        });
        return stageAccountingItemVoList;
    }

    /**
     * 创建科目余额数据
     *
     * @param companyId
     * @param year
     * @param month
     * @param staffInfo
     * @param grade
     */
    @Transactional
    public void createStageAccountingItemValue(Long companyId, int year, int month, StaffInfo staffInfo, Integer grade) {
        //1.删除当月科目余额表
        stageAccountingItemValueMapper.delete(Wrappers.<StageAccountingItemValue>lambdaQuery().eq(StageAccountingItemValue::getFkCompanyId, companyId).eq(StageAccountingItemValue::getYear, year).eq(StageAccountingItemValue::getMonth, month));
        //2.账套科目配置
        List<CompanyAccountingItemVo> companyAccountingItemList = companyAccountingItemMapper.getCompanyAccountingConfigure(companyId, null, grade);
        if (GeneralTool.isNotEmpty(companyAccountingItemList)) {
            List<StageAccountingItemValue> stageAccountingItemValueList = new ArrayList<>();
            for (CompanyAccountingItemVo companyAccountingItemVo : companyAccountingItemList) {
                AccountingItem accountingItem = accountingItemMapper.selectById(companyAccountingItemVo.getFkAccountingItemId());
                //所有科目id(包含下级所有科目)
                List<Long> accountingItemList = accountingItemService.getChildrenAccountingItems(Collections.singletonList(accountingItem.getId()));
                accountingItemList.add(accountingItem.getId());

                StageAccountingItemValue stageAccountingItemValue =  new StageAccountingItemValue();
                stageAccountingItemValue.setFkCompanyId(companyAccountingItemVo.getFkCompanyId());
                stageAccountingItemValue.setYear(year);
                stageAccountingItemValue.setMonth(month);
                stageAccountingItemValue.setFkAccountingItemId(companyAccountingItemVo.getFkAccountingItemId());
                stageAccountingItemValue.setDirectionValue(null);
                //当月第一天
                Date firstDayOfMonth = GetDateUtil.getBeginTime(year, month);
                //获取当月的前一天时间
                Date lastDayOfPrevMonth = GetDateUtil.getYesterdayDate(firstDayOfMonth);
                //当月最后一天
                Date lastDayOfMonth = GetDateUtil.getLastDayOfMonth(firstDayOfMonth);
                //借方期初数DR 计算当月第一天的前一天
                stageAccountingItemValue.setAmountDrOpeningBalance(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, null, lastDayOfPrevMonth, null, null, 2));
                //贷方期初数CR 计算当月第一天的前一天
                stageAccountingItemValue.setAmountCrOpeningBalance(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, null, lastDayOfPrevMonth, null, null, 3));
                //贷方期末数DR 计算当月最后一天
                stageAccountingItemValue.setAmountDrClosingBalance(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, null, lastDayOfMonth, null, null, 2));
                //贷方期末数CR 计算当月最后一天
                stageAccountingItemValue.setAmountCrClosingBalance(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, null, lastDayOfMonth, null, null, 3));
                //借方本期发生额：当月第一天和最后一天 dr累加
                stageAccountingItemValue.setAmountDr(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, firstDayOfMonth, lastDayOfMonth, null, null, 2));
                //贷方本期发生额：当月第一天和最后一天 cr累加
                stageAccountingItemValue.setAmountCr(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, firstDayOfMonth, lastDayOfMonth, null, null, 3));

                Date startTime = GetDateUtil.makeDate(year, 1, 1);
                Date endTime = GetDateUtil.getLastDayOfMonth(GetDateUtil.makeDate(year, month, 1));
                //借方本年累计发生额:结束时间所在那一年，并且不大于结束月份的发生额
                stageAccountingItemValue.setAmountDrSum(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, startTime, endTime, null, null, 2));
                stageAccountingItemValue.setAmountCrSum(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, startTime, endTime, null, null, 3));
                stageAccountingItemValue.setGmtCreateUser(staffInfo.getLoginId());
                stageAccountingItemValue.setGmtCreate(new Date());
                stageAccountingItemValueList.add(stageAccountingItemValue);
            }
            stageAccountingItemValueMapper.insertBatch(stageAccountingItemValueList);
        }

    }

    /**
     * 重新统计科目余额汇总表
     *
     * @param reCalculateSubjectBalanceSummaryDto
     */
    @Override
    public void reCalculateSubjectBalanceSummary(ReCalculateSubjectBalanceSummaryDto reCalculateSubjectBalanceSummaryDto) {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        //插入异步任务信息
        StageValueTaskQueue stageValueTaskQueue = new StageValueTaskQueue();
        stageValueTaskQueue.setFkTableName(TableEnum.FINANCE_STAGE_ACCOUNTING_ITEM_VALUE.key);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        stageValueTaskQueue.setStartYearMonth(sdf.format(reCalculateSubjectBalanceSummaryDto.getStartTime()));
        stageValueTaskQueue.setFkCompanyIds(StringUtils.join(reCalculateSubjectBalanceSummaryDto.getCompanyIds(), ","));
        stageValueTaskQueue.setStartTime(new Date());
        stageValueTaskQueue.setStatus(1);
        stageValueTaskQueue.setGmtCreateUser(staffInfo.getLoginId());
        stageValueTaskQueue.setGmtCreate(new Date());
        stageValueTaskQueueMapper.insert(stageValueTaskQueue);
        stageAccountingItemService.reCalculateSubjectBalanceSummaryAsync(reCalculateSubjectBalanceSummaryDto, staffInfo, stageValueTaskQueue);
    }

    /**
     * 异步创建
     */
    @Async
    @Transactional
    public void reCalculateSubjectBalanceSummaryAsync(ReCalculateSubjectBalanceSummaryDto reCalculateSubjectBalanceSummaryDto, StaffInfo staffInfo, StageValueTaskQueue stageValueTaskQueue) {
        try {
            List<Long> companyIds = reCalculateSubjectBalanceSummaryDto.getCompanyIds();
            Date startTime = reCalculateSubjectBalanceSummaryDto.getStartTime();
            // 获取系统当前时间
            Date currentDate = new Date();
            // 获取从传入时间到当前时间的所有YearMonth集合
            List<YearMonth> yearMonthList = GetDateUtil.getYearMonthRange(startTime, currentDate);
            for (Long companyId : companyIds) {
                for (YearMonth yearMonth : yearMonthList) {
                    createStageAccountingItemValue(companyId, yearMonth.getYear(), yearMonth.getMonthValue(), staffInfo, null);
                }
            }
            stageValueTaskQueue.setEndTime(new Date());
            stageValueTaskQueue.setStatus(2);
            stageValueTaskQueue.setGmtCreateUser(staffInfo.getLoginId());
            stageValueTaskQueue.setGmtCreate(new Date());
            stageValueTaskQueueMapper.updateById(stageValueTaskQueue);
        } catch (Exception e) {
            e.printStackTrace();
            updateTaskStatus(stageValueTaskQueue, e.getMessage(), staffInfo);
            throw new RuntimeException(e);
        }

    }

    /**
     * 使用REQUIRES_NEW确保创建新事务
     * @param queue
     * @param message
     * @param staff
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTaskStatus(StageValueTaskQueue queue, String message, StaffInfo staff) {
        queue.setEndTime(new Date());
        queue.setStatus(3);
        queue.setMessage(message);
        queue.setGmtCreateUser(staff.getLoginId());
        queue.setGmtCreate(new Date());
        stageValueTaskQueueMapper.updateById(queue);
    }

}
