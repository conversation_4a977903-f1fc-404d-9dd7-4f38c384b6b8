package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.service.IAiService;
import com.get.financecenter.vo.ExpenseReimbursementVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "Ai管理")
@RestController
@RequestMapping("finance/ai")
public class AiController {
    @Resource
    private IAiService aiService;

    //根据token 查询自己的费用报销单和差旅报销单
    @ApiOperation(value = "根据token 查询自己的费用报销单和差旅报销单", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/Ai管理/查询自己的费用报销单和差旅报销单")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getMyExpenseReimbursement")
    public ResponseBo<ExpenseReimbursementVo> getMyExpenseReimbursement() {
        return new ResponseBo<>(aiService.getMyExpenseReimbursement());
    }
}
