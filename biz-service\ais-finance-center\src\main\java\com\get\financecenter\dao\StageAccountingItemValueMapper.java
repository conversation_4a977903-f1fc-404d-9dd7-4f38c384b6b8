package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.dto.StageAccountingItemDto;
import com.get.financecenter.entity.StageAccountingItemValue;
import com.get.financecenter.vo.StageAccountingItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StageAccountingItemValueMapper extends BaseMapper<StageAccountingItemValue> {

    void insertBatch(List<StageAccountingItemValue> stageAccountingItemValueList);

    /**
     * 查询科目余额汇总表
     * @param stageAccountingItemDto
     * @return
     */
    List<StageAccountingItemVo> getStageAccountingItemValueList(@Param("stageAccountingItemDto") StageAccountingItemDto stageAccountingItemDto);


    List<StageAccountingItemValue> selectByFkAccountingItemId(@Param("fkAccountingItemId") Long fkAccountingItemId);
}
