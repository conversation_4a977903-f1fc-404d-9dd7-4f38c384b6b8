<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.StageAccountingItemValueMapper">

    <insert id="insertBatch">
        INSERT INTO `ais_finance_center`.`m_stage_accounting_item_value` (`fk_company_id`, `year`, `month`, `fk_accounting_item_id`, `direction_value`, `amount_dr_opening_balance`, `amount_cr_opening_balance`, `amount_dr`, `amount_cr`, `amount_dr_closing_balance`, `amount_cr_closing_balance`, `amount_dr_sum`, `amount_cr_sum`, `gmt_create`, `gmt_create_user`) VALUES
        <foreach collection="stageAccountingItemValueList" item="item" separator=",">
            (#{item.fkCompanyId}, #{item.year}, #{item.month}, #{item.fkAccountingItemId}, #{item.directionValue}, #{item.amountDrOpeningBalance}, #{item.amountCrClosingBalance}, #{item.amountDr}, #{item.amountCr}, #{item.amountDrClosingBalance}, #{item.amountCrClosingBalance}, #{item.amountDrSum}, #{item.amountCrSum}, #{item.gmtCreate}, #{item.gmtCreateUser})
        </foreach>
    </insert>

    <select id="getStageAccountingItemValueList" resultType="com.get.financecenter.vo.StageAccountingItemVo">
        SELECT
        mai.id AS fkAccountingItemId,
        mai.type,
        CONCAT(mai.CODE, '-', mai.code_name) AS accountingItemName,
        mai.direction,
        mai.grade,
        openingBalance.amount_dr_opening_balance AS amountDrOpeningBalance,
        openingBalance.amount_cr_opening_balance AS amountCrOpeningBalance,
        closingBalance.amount_dr_closing_balance AS amountDrClosingBalance,
        closingBalance.amount_cr_closing_balance AS amountCrClosingBalance,
        amountSum.amountDr,
        amountSum.amountCr,
        closingBalance.amount_dr_sum AS amountDrSum,
        closingBalance.amount_cr_sum AS amountCrSum
        FROM
        m_stage_accounting_item_value AS msa
        INNER JOIN m_accounting_item AS mai ON mai.id = msa.fk_accounting_item_id
        <!-- 期初数 -->
        LEFT JOIN (
        SELECT
        msa.fk_accounting_item_id,
        msa.amount_dr_opening_balance,
        msa.amount_cr_opening_balance
        FROM
        m_stage_accounting_item_value AS msa
        WHERE
        msa.fk_company_id = #{stageAccountingItemDto.companyId}
        AND year = YEAR(#{stageAccountingItemDto.startTime})
        AND month = MONTH(#{stageAccountingItemDto.startTime})
        ) AS openingBalance ON openingBalance.fk_accounting_item_id = msa.fk_accounting_item_id
        <!-- 期末数 -->
        LEFT JOIN (
        SELECT
        msa.fk_accounting_item_id,
        msa.amount_dr_closing_balance,
        msa.amount_cr_closing_balance,
        msa.amount_dr_sum,
        msa.amount_cr_sum
        FROM
        m_stage_accounting_item_value AS msa
        WHERE
        year = YEAR(#{stageAccountingItemDto.endTime})
        AND month = MONTH(#{stageAccountingItemDto.endTime})
        ) AS closingBalance ON closingBalance.fk_accounting_item_id = msa.fk_accounting_item_id
        <!-- 本期发生额 -->
        LEFT JOIN (
        SELECT
        msa.fk_accounting_item_id,
        SUM(amount_dr) AS amountDr,
        SUM(amount_cr) AS amountCr
        FROM
        m_stage_accounting_item_value AS msa
        WHERE
        msa.fk_company_id = #{stageAccountingItemDto.companyId}
        AND
        (
        (msa.year > YEAR(#{stageAccountingItemDto.startTime}))
        OR (msa.year = YEAR(#{stageAccountingItemDto.startTime}) AND msa.month >= MONTH(#{stageAccountingItemDto.startTime}))
        )
        AND (
        (msa.year <![CDATA[< ]]> YEAR(#{stageAccountingItemDto.endTime}))
        OR (msa.year = YEAR(#{stageAccountingItemDto.endTime}) AND msa.month <![CDATA[<= ]]> MONTH(#{stageAccountingItemDto.endTime}))
        )
        GROUP BY msa.fk_accounting_item_id
        )amountSum ON amountSum.fk_accounting_item_id = msa.fk_accounting_item_id


        WHERE
        msa.fk_company_id = #{stageAccountingItemDto.companyId}
        AND mai.grade <![CDATA[<= ]]> #{stageAccountingItemDto.grade}
        AND (
        (msa.year > YEAR(#{stageAccountingItemDto.startTime}))
        OR (msa.year = YEAR(#{stageAccountingItemDto.startTime}) AND msa.month >= MONTH(#{stageAccountingItemDto.startTime}))
        )
        AND (
        (msa.year <![CDATA[< ]]> YEAR(#{stageAccountingItemDto.endTime}))
        OR (msa.year = YEAR(#{stageAccountingItemDto.endTime}) AND msa.month <![CDATA[<= ]]> MONTH(#{stageAccountingItemDto.endTime}))
        )
        GROUP BY mai.id
        ORDER BY mai.code
    </select>

    <select id="selectByFkAccountingItemId" resultType="com.get.financecenter.entity.StageAccountingItemValue">
        SELECT
        *
        FROM ais_finance_center.m_stage_accounting_item_value
        WHERE fk_accounting_item_id = #{fkAccountingItemId}
    </select>
</mapper>