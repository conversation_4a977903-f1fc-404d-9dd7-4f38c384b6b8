<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.StageProfitAndLossValueMapper">

    <insert id="insertBatch">
        INSERT INTO ais_finance_center.m_stage_profit_and_loss_value (`fk_company_id`, `year`, `month`, `item_index`, `fk_company_profit_and_loss_item_id`, `title`, `direction_value`, `amount_opening_balance`, `amount_dr`, `amount_cr`, `amount_closing_balance`, `amount`, `is_sum`, `gmt_create`, `gmt_create_user`) VALUES
        <foreach collection="stageProfitAndLossValueList" item="item" separator=",">
            (#{item.fkCompanyId}, #{item.year}, #{item.month}, #{item.itemIndex}, #{item.fkCompanyProfitAndLossItemId}, #{item.title}, #{item.directionValue}, #{item.amountOpeningBalance}, #{item.amountDr}, #{item.amountCr}, #{item.amountClosingBalance}, #{item.amount}, #{item.isSum}, #{item.gmtCreate}, #{item.gmtCreateUser})
        </foreach>
    </insert>

    <select id="getProfitAndLossStatementTitle" resultType="com.get.financecenter.vo.ProfitAndLossStatementItemVo">
        SELECT
            msp.title, mcp.color_code
        FROM
            ais_finance_center.m_stage_profit_and_loss_value AS msp
        LEFT JOIN ais_finance_center.m_company_profit_and_loss_item AS mcp ON msp.fk_company_profit_and_loss_item_id = mcp.id
        where msp.fk_company_id = #{probAndLossStatementDto.companyId}
        AND msp.year = #{probAndLossStatementDto.year}
        GROUP BY msp.title
        ORDER BY msp.item_index asc, msp.id desc
    </select>
    <select id="getStageProfitAndLossValueSumList" resultType="com.get.financecenter.entity.StageProfitAndLossValue">
        SELECT
            *
        FROM
            m_stage_profit_and_loss_value
        WHERE
            is_sum = 1
                AND fk_company_id = #{companyId}
                AND YEAR = #{year}
                AND MONTH = #{month}
    </select>

    <select id="selectByFkCompanyProfitAndLossItemId" resultType="com.get.financecenter.entity.StageProfitAndLossValue">
        SELECT
        *
        FROM ais_finance_center.m_stage_profit_and_loss_value
        WHERE fk_company_profit_and_loss_item_id = #{fkCompanyProfitAndLossItemId}
    </select>
</mapper>